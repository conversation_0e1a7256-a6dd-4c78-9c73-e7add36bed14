export 'app_event.dart';
export 'app_state.dart';

import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/logger.dart';

import '../app_config.dart';
import 'app_event.dart';
import 'app_state.dart';

class AppBloc extends Bloc<AppEvent, AppState> {
  bool _isAuthenticated = false;
  bool get isAuthenticated => _isAuthenticated;
  
  // Store the return route for after login
  String? _returnRoute;

  AppBloc() : super(const AppState.initial()) {
    on<AppEvent>((event, emit) async {
      await event.map(
        appStarted: (_) => _onAppStarted(emit),
        loginRequested: (e) => _onLoginRequested(e.token, e.user, emit),
        logoutRequested: (_) => _onLogoutRequested(emit),
      );
    });
  }

  Future<void> _onAppStarted(Emitter<AppState> emit) async {
    emit(const AppState.loading());
    _setEnvironment();

    try {
      await Future.delayed(const Duration(seconds: 1));
      final bool isAuthenticated = await checkAuthStatus();
      emit(AppState.loaded(isAuthenticated: isAuthenticated));
    } catch (e) {
      emit(const AppState.loaded(isAuthenticated: false));
    }
  }

  Future<void> _onLogoutRequested(Emitter<AppState> emit) async {
    try {
      await logout();
      final newState = state.maybeMap(
        loaded: (loaded) => loaded.copyWith(isAuthenticated: false),
        orElse: () => const AppState.loaded(isAuthenticated: false),
      );
      emit(newState);
    } catch (e) {
      LogMessage.p('Failed to logout user: $e');
    }
  }

  Future<void> _onLoginRequested(
      String token, Map<String, dynamic>? user, Emitter<AppState> emit) async {
    try {
      await _authenticateUser(token, user);
      final newState = state.maybeMap(
        loaded: (loaded) => loaded.copyWith(isAuthenticated: true),
        orElse: () => const AppState.loaded(isAuthenticated: true),
      );
      emit(newState);
    } catch (e) {
      LogMessage.p('Failed to login user: $e');
    }
  }

  Future<void> _authenticateUser(
    String token,
    Map<String, dynamic>? user,
  ) async {
    _isAuthenticated = true;

    await AppPreferences.setToken(token);
    await AppPreferences.setLoginStatus(true);
    if (user != null) {
      await AppPreferences.setUserdata(jsonEncode(user));
    }
  }
  
  // Set the return route to navigate to after successful login
  void setReturnRoute(String route) {
    _returnRoute = route;
  }
  
  // Get and clear the return route
  String? getAndClearReturnRoute() {
    final route = _returnRoute;
    _returnRoute = null;
    return route;
  }

  Future<void> logout() async {
    _isAuthenticated = false;
    await AppPreferences.setLoginStatus(false);
    await AppPreferences.setToken('');
    await AppPreferences.setUserdata('');
  }

  Future<bool> checkAuthStatus() async {
    _isAuthenticated = AppPreferences.getLoginStatus() ?? false;
    return _isAuthenticated;
  }

  static void _setEnvironment() {
    const String envName = String.fromEnvironment(
      'ENV',
      defaultValue: 'dev',
    );

    switch (envName) {
      case 'prod':
        AppConfig.environment = Environment.production;
        break;
      case 'staging':
        AppConfig.environment = Environment.staging;
        break;
      default:
        AppConfig.environment = Environment.development;
        break;
    }
  }
}
