# Rozana E-commerce App

A Flutter e-commerce application built with clean architecture principles.

## Project Overview

Rozana is a modern e-commerce application built with Flutter. The app follows clean architecture principles to ensure maintainability, testability, and scalability. It features a comprehensive service layer, domain-driven design, and a clear separation of concerns.

## Architecture

The project follows a clean architecture approach with the following layers:

### 1. Presentation Layer
- **Screens**: User interface components
- **Widgets**: Reusable UI components
- **State Management**: Manages UI state and business logic

### 2. Domain Layer
- **Entities**: Core business objects
- **Use Cases**: Application-specific business rules
- **Repositories (Interfaces)**: Abstract data source access

### 3. Data Layer
- **Repositories (Implementations)**: Concrete implementations of domain repositories
- **Models**: Data transfer objects and mappers
- **Data Sources**: Remote and local data sources

### 4. Core Services Layer
- **Auth Service**: Authentication and user management
- **API Service**: Network communication
- **Storage Service**: Local data persistence
- **Notification Service**: In-app notifications and alerts
- **Connectivity Service**: Network connectivity monitoring
- **Logger Service**: Application logging

## Project Structure

```
lib/
├── main.dart              # Application entry point
├── config/                # App configuration
│   ├── constants/         # App constants
│   ├── injection/         # Dependency injection
│   ├── routes/            # Navigation routes
│   └── theme/             # App theme configuration
├── core/                  # Core functionality
│   ├── constants/         # Core constants
│   ├── errors/            # Error handling
│   ├── network/           # Network utilities
│   ├── theme/             # Theme definitions
│   └── utils/             # Utility functions
├── presentation/          # UI layer
│   ├── screens/           # App screens
│   │   ├── auth/          # Authentication screens
│   │   ├── home/          # Home screen
│   │   └── splash/        # Splash screen
│   └── widgets/           # Reusable UI components
│       ├── address/       # Address-related widgets
│       ├── buttons/       # Button components
│       ├── cards/         # Card components
│       ├── carousel/      # Carousel components
│       ├── category/      # Category-related widgets
│       ├── feedback/      # User feedback widgets
│       ├── inputs/        # Input field components
│       ├── layout/        # Layout components
│       └── product/       # Product-related widgets
└── services/              # Business logic and services
    ├── api/               # API services
    ├── auth/              # Authentication services
    ├── network/           # Network services
    ├── notification/      # Notification services
    ├── search/            # Search functionality
    ├── storage/           # Local storage services
    └── utils/             # Service utilities
```

## Core Services

### Authentication Service
- **Interface**: `AuthService`
- **Implementation**: `AuthServiceImpl`
- **Functionality**: 
  - User authentication (login, register)
  - Password management
  - Session management
  - Authentication state stream
  - Auth headers for API requests

### API Service
- **Interface**: `ApiService`
- **Implementation**: `ApiServiceImpl`
- **Functionality**: 
  - HTTP methods (GET, POST, PUT, DELETE)
  - Authentication header integration
  - Error handling
  - Response parsing

### Storage Service
- **Interface**: `StorageService`
- **Implementation**: `SharedPreferencesService`
- **Functionality**: 
  - Key-value storage
  - Object serialization/deserialization
  - User data persistence

### User Storage Service
- **Functionality**: 
  - Token management
  - User data storage
  - Session management

### Notification Service
- **Interface**: `NotificationService`
- **Implementation**: `NotificationServiceImpl`
- **Functionality**: 
  - Snackbar notifications
  - Toast messages
  - Dialog boxes
  - Loading indicators

### Connectivity Service
- **Interface**: `ConnectivityService`
- **Implementation**: `ConnectivityServiceImpl` (currently a mock)
- **Functionality**: 
  - Network connectivity monitoring
  - Connectivity state stream

### Logger Service
- **Interface**: `LoggerService`
- **Implementation**: `LoggerServiceImpl`
- **Functionality**: 
  - Environment-aware logging
  - Log level management
  - Structured logging

## Base Classes

### Repositories
- **Base Repository**: Abstract interface for CRUD operations
- **Base Repository Implementation**: Common repository functionality

### Entities and Models
- **Base Entity**: Common entity properties and behavior
- **Base Model**: JSON serialization/deserialization

### Use Cases
- **Base Use Case**: Common use case interface

## Error Handling

The application uses a comprehensive error handling approach:

- **Failure Classes**: Hierarchy of failure types
- **Either Type**: Functional error handling with dartz
- **Repository Error Handling**: Consistent error handling in repositories
- **Service Error Handling**: Service-specific error handling

## Getting Started

### Prerequisites
- Flutter SDK: 3.32.2 (stable channel)
- Dart SDK: 3.5.2
- Android Studio / VS Code

### Installation

1. Clone the repository
```bash
git clone repo-url
```

2. Install dependencies
```bash
flutter pub get
```

3. Run the app
```bash
flutter run
```

## Dependencies

- **http**: Network requests
- **shared_preferences**: Local storage
- **get_it**: Dependency injection
- **dartz**: Functional programming
- **equatable**: Value equality

## Future Improvements

- Implement real connectivity service using connectivity_plus
- Add comprehensive unit and integration tests
- Implement feature-specific repositories and use cases
- Add token refresh mechanism
- Implement login screen and complete authentication flow
