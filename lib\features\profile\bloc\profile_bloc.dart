export 'profile_event.dart';
export 'profile_state.dart';

import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/features/location/services/adress_services.dart';

import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc() : super(const ProfileState.initial()) {
    on<ProfileEvent>((event, emit) async {
      await event.map(
        loadUserData: (e) => _mapLoadUserDataToState(emit),
        logout: (e) => _mapLogoutToState(emit),
        loadAddressCount: (e) => _mapLoadAddressCountToState(emit),
      );
    });
  }

  Future<void> _mapLoadUserDataToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      String? userJson = AppPreferences.getUserdata();
      int addressCount = await _getAddressCount();
      if (userJson != null && userJson.isNotEmpty) {
        Map<String, dynamic> userData = jsonDecode(userJson);
        emit(
          ProfileState.loaded(
            userName:
                userData['displayName'] ?? userData['phoneNumber'] ?? 'No Name',
            userEmail: userData['email'] ?? 'No Email',
            userGender: userData['gender'] ?? 'Not specified',
            addressCount: addressCount,
          ),
        );
      } else {
        emit(ProfileState.loaded(
          userName: 'Guest User',
          userEmail: 'N/A',
          userGender: 'Not specified',
          addressCount: addressCount,
        ));
      }
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to load user data: $e'));
    }
  }

  Future<void> _mapLogoutToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      getIt<AppBloc>().add(AppEvent.logoutRequested());
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to logout: $e'));
    }
  }

  Future<void> _mapLoadAddressCountToState(Emitter<ProfileState> emit) async {
    try {
      int addressCount = await _getAddressCount();
      // Keep previous user data if available
      final currentState = state;
      currentState.mapOrNull(
        loaded: (loadedState) {
          emit(ProfileState.loaded(
            userName: loadedState.userName,
            userEmail: loadedState.userEmail,
            userGender: loadedState.userGender,
            addressCount: addressCount,
          ));
        },
      );
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to load address count: $e'));
    }
  }

  Future<int> _getAddressCount() async {
    final addresses = await AddressService().getAllAddresses();
    return addresses.length;
  }
}
