import 'dart:convert';

import 'package:amplitude_flutter/amplitude.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../app/bloc/app_bloc.dart';
import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/logger.dart';
import '../../../../data/models/cart_item_model.dart';
import '../../../cart/bloc/cart_event.dart';
import '../../../cart/utils/cart_utils.dart';

class ProductDetailPage extends StatefulWidget {
  final Map<String, dynamic> productData;

  const ProductDetailPage({
    super.key,
    required this.productData,
  });

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  @override
  Widget build(BuildContext context) {
    final String name = widget.productData['name'] ?? '--';
    final String productId = widget.productData['id'] ?? '';
    final String description =
        widget.productData['description'] ?? 'No description available';
    final String imageUrl = (widget.productData['imageUrl'] is List)
        ? widget.productData['imageUrl'][0]
        : widget.productData['imageUrl'] ?? '';
    final double price = widget.productData['price']?.toDouble() ?? 0.0;
    final double originalPrice =
        widget.productData['originalPrice']?.toDouble() ?? price;
    final double rating = widget.productData['rating']?.toDouble() ?? 0.0;
    final int reviewCount = widget.productData['reviewCount']?.toInt() ?? 0;
    final int discountPercentage =
        widget.productData['discountPercentage']?.toInt() ?? 0;
    final bool isFeatured = widget.productData['isFeatured'] ?? false;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        foregroundColor: Colors.black,
        title: Text(name, style: const TextStyle(color: Colors.black)),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: IconButton(
              icon: const Icon(Icons.share),
              onPressed: () => _createAndShareDeepLink(
                  productId, name, imageUrl, description, widget.productData),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Stack(
                    children: [
                      AspectRatio(
                        aspectRatio: 1,
                        child: Image.network(imageUrl, fit: BoxFit.cover),
                      ),
                      if (isFeatured)
                        Positioned(
                          top: 16,
                          right: 16,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.redAccent,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Text(
                              'FEATURED',
                              style:
                                  TextStyle(color: Colors.white, fontSize: 12),
                            ),
                          ),
                        ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          name,
                          style: const TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87),
                        ),
                      ),
                      if (discountPercentage > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green[50],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '-$discountPercentage%',
                            style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 10),

                  Row(
                    children: [
                      Text(
                        '₹${price.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(width: 10),
                      if (originalPrice > price)
                        Text(
                          '₹${originalPrice.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 10),

                  // Ratings
                  Row(
                    children: [
                      ...List.generate(5, (index) {
                        if (index < rating.floor()) {
                          return const Icon(Icons.star,
                              size: 18, color: Colors.amber);
                        } else {
                          return const Icon(Icons.star_border,
                              size: 18, color: Colors.grey);
                        }
                      }),
                      const SizedBox(width: 8),
                      Text('$rating ($reviewCount reviews)',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black26,
                          )),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Description
                  const Text(
                    "Product Description",
                    style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: const TextStyle(
                        fontSize: 15, height: 1.5, color: Colors.black54),
                  ),
                ],
              ),
            ),
          ),

          // Bottom Buttons
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Row(
                children: [
                  Expanded(
                    child: BlocBuilder<CartBloc, CartState>(
                        builder: (context, state) {
                      final quantity =
                          CartUtils.getItemQuantity(productId, state.cart);
                      return quantity > 0
                          ? _buildQuantitySelector(
                              productId, name, imageUrl, price)
                          : GestureDetector(
                              onTap: () {
                                HapticFeedback.lightImpact();

                                context.read<CartBloc>().add(CartEvent.addItem(
                                        item: CartItemModel(
                                      productId: productId,
                                      name: name,
                                      price: price,
                                      imageUrl: imageUrl,
                                      quantity: 1,
                                      unit:
                                          'item', // Default unit, can be customized
                                      discountedPrice: price,
                                      facilityId: widget
                                          .productData['facilityId']
                                          ?.toString(),
                                      facilityName: widget
                                          .productData['facilityName']
                                          ?.toString(),
                                      skuID:
                                          widget.productData['sku']?.toString(),
                                    )));
                                final Amplitude amplitude =
                                    Amplitude.getInstance(
                                        instanceName: "default");
                                amplitude.logEvent(
                                  'AddToCart',
                                  eventProperties: {
                                    'id': productId,
                                    'item': name,
                                    'price': price,
                                  },
                                );
                              },
                              child: Container(
                                height: 48,
                                decoration: BoxDecoration(
                                  color: Colors.transparent,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: AppColors.primary,
                                    width: 1,
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: const Text(
                                  "Add to Cart",
                                  style: TextStyle(
                                      color: AppColors.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            );
                    }),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _placeOrder(CartItemModel(
                          productId: productId,
                          name: name,
                          price: price,
                          imageUrl: imageUrl,
                          quantity: 1,
                          unit: 'item', // Default unit, can be customized
                          discountedPrice: price,
                          facilityId:
                              widget.productData['facilityId']?.toString(),
                          facilityName:
                              widget.productData['facilityName']?.toString(),
                          skuID: widget.productData['sku']?.toString(),
                        ));
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryDark,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      child: const Text("Buy Now"),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector(
      String productId, String name, String imageUrl, double price) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        final quantity = CartUtils.getItemQuantity(productId, state.cart);

        return Container(
          height: 48,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.primary,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Minus button
              InkWell(
                splashColor: Colors.transparent,
                onTap: () async {
                  HapticFeedback.lightImpact();
                  final cartItemId =
                      CartUtils.getCartItemId(productId, state.cart);

                  if (cartItemId != null) {
                    if (quantity > 1) {
                      context.read<CartBloc>().add(CartEvent.updateQuantity(
                          cartItemId, (quantity - 1).toInt()));
                    } else if (quantity == 1) {
                      context
                          .read<CartBloc>()
                          .add(CartEvent.removeItem(cartItemId));
                    }
                    // Force rebuild after async operation
                    if (mounted) {
                      setState(() {});
                    }
                  }
                },
                borderRadius:
                    const BorderRadius.horizontal(left: Radius.circular(8)),
                child: Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.remove,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
              ),

              // Quantity display
              Container(
                alignment: Alignment.center,
                width: 28,
                child: Text(
                  quantity.toString(),
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),

              // Plus button
              InkWell(
                splashColor: Colors.transparent,
                onTap: () async {
                  HapticFeedback.lightImpact();
                  if (quantity == 0) {
                    context.read<CartBloc>().add(CartEvent.addItem(
                            item: CartItemModel(
                          productId: productId,
                          name: name,
                          price: price,
                          imageUrl: imageUrl,
                          quantity: 1,
                          unit: 'item', // Default unit, can be customized
                          discountedPrice: price,
                          facilityId:
                              widget.productData['facilityId']?.toString(),
                          facilityName:
                              widget.productData['facilityName']?.toString(),
                          skuID: widget.productData['sku']?.toString(),
                        )));
                  } else {
                    final cartItemId =
                        CartUtils.getCartItemId(productId, state.cart);
                    if (cartItemId != null) {
                      context.read<CartBloc>().add(CartEvent.updateQuantity(
                          cartItemId, (quantity + 1).toInt()));
                    }
                  }
                  // widget.onAddToCart?.call();
                  // Force rebuild after async operation
                  if (mounted) {
                    setState(() {});
                  }
                },
                borderRadius:
                    const BorderRadius.horizontal(right: Radius.circular(8)),
                child: Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.add,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  final Map<String, dynamic> _checkoutData = {
    'paymentMethod': 'Cash on Delivery',
    'address': {
      'fullName': 'John Doe',
      'phoneNumber': '9876543210',
      'addressLine1': '123 Main Street',
      'addressLine2': 'Apartment 4B',
      'city': 'Mumbai',
      'state': 'Maharashtra',
      'pincode': '400001',
      'addressType': 'Home',
    },
    'deliverySlot': 'standard',
  };

  Future<void> _placeOrder(CartItemModel data) async {
    // Check if user is authenticated before proceeding with checkout
    final appBloc = getIt<AppBloc>();
    if (!appBloc.isAuthenticated) {
      // Navigate to login screen with return route information
      context.push(RouteNames.login, extra: {'returnRoute': RouteNames.cart});
      return;
    }

    // setState(() {
    //   _isProcessingOrder = true;
    // });

    try {
      // Simulate order placement with a delay
      await Future.delayed(const Duration(seconds: 2));

      // Generate order ID
      final orderId = 'ORD-${DateTime.now().millisecondsSinceEpoch}';

      // Create order data
      final orderData = {
        'items': [data],
        'address': _checkoutData['address'],
        'paymentMethod': _checkoutData['paymentMethod'],
        'total': data.totalPrice,
        'subtotal': data.price,
        'discount': data.discountedPrice,
        // 'deliveryFee': data.,
        'itemCount': data.quantity,
        'orderDate': DateTime.now().toIso8601String(),
      };

      // Clear cart after successful order
      getIt<CartBloc>().add(CartEvent.clear());

      // Navigate to order success screen
      if (mounted) {
        context.pushReplacement(
          RouteNames.orderSuccess,
          extra: {
            'orderId': orderId,
            'orderData': orderData,
          },
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to place order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {}
  }
}

/// Generates a Branch deep link and opens the native share sheet.
Future<void> _createAndShareDeepLink(
    String productId,
    String name,
    String imageUrl,
    String description,
    Map<String, dynamic> productData) async {
  // 1. Define the Branch Universal Object (BUO)
  // This represents the content you want to share.
  BranchUniversalObject buo = BranchUniversalObject(
    canonicalIdentifier:
        'product-detail/${productId}_${DateTime.now().millisecondsSinceEpoch}',
    title: name,
    imageUrl: imageUrl,
    contentDescription: description,
    // Custom metadata will be passed to the app when the link is opened.
    contentMetadata: BranchContentMetaData()
        .addCustomMetadata('product', jsonEncode(productData))
        .addCustomMetadata('screen', RouteNames.productDetail)
        .addCustomMetadata('source_feature', 'in_app_share'),
    keywords: ['exclusive', 'early access', 'special'],
    // Content indexing: Helps search engines find your app content.
    locallyIndex:
        true, // For on-device indexing (iOS Spotlight, Android App Indexing)
    publiclyIndex: true, // For public search engine indexing
  );

  // 2. Define Branch Link Properties
  // These define the analytics and behavior of the link.
  BranchLinkProperties lp = BranchLinkProperties(
    channel:
        'FlutterAppShare', // Where the link is shared (e.g., WhatsApp, Email)
    feature:
        'share_product', // The purpose of the link (e.g., sharing, referral)
    campaign: 'product_promotion', // Your marketing campaign name
    stage: 'beta', // Current stage of the user journey
    tags: ['app_preview', 'beta', 'deep_link_test'],
    alias: 'shared-link/${productId}_${DateTime.now().millisecondsSinceEpoch}',
  );

  // 4. Generate the short URL
  BranchResponse response =
      await FlutterBranchSdk.getShortUrl(buo: buo, linkProperties: lp);

  if (response.success) {
    LogMessage.l('Branch SDK: Link generated: ${response.result}',
        subTag: 'SHARE::', color: Colors.green);

    await Share.share(
      response.result,
      // subject: 'Checkout this product: $name',
    );
  } else {
    LogMessage.l(
        'Branch SDK: Error generating link: ${response.errorCode} - ${response.errorMessage}',
        subTag: 'SHARE::',
        color: Colors.red);
  }
}
