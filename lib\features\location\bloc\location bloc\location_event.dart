import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_event.freezed.dart';

@freezed
class LocationEvent with _$LocationEvent {
  const factory LocationEvent.started() = _Started;
  const factory LocationEvent.refreshLocation() = _RefreshLocation;
  // const factory LocationEvent.getCurrentLocation() = _GetCurrentLocation;
  // const factory LocationEvent.getAddressFromCoordinates(double latitude, double longitude) = _GetAddressFromCoordinates;
}
