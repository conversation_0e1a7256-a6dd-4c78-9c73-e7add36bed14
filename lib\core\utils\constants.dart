//List loading types>
enum ListLoaderType { initial, refresh, pagination }

class AppConstants {
  // App related
  static const String appName = 'TEST';
  static const String appVersion = '1.0.0';

  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  /// Default language code
  static const String defaultLanguage = 'en';

  /// Supported languages
  static const List<String> supportedLanguages = ['en', 'hi', 'ar'];

  /// Cache duration in hours
  static const int cacheDuration = 24;
}
