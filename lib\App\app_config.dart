import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/core/network/api_endpoints.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:amplitude_flutter/amplitude.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform, kIsWeb;
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/core/services/app_verification_service.dart';

/// Environment types
enum Environment {
  development,
  staging,
  production,
}

/// Application configuration settings
class AppConfig {
  /// Current environment
  static Environment environment = Environment.development;

  static Future<void> init() async {
    // Set system overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Set preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    await AppPreferences.init();

    //Branch sdk initialization
    await FlutterBranchSdk.init(
        enableLogging: true,
        branchAttributionLevel: BranchAttributionLevel.FULL);

    // Initialize Amplitude for analytics
    final Amplitude amplitude = Amplitude.getInstance(instanceName: "default");

    amplitude.init("fa4004be40bfd5c03e30e6725351c87e");

    // Initialize Firebase (will use GoogleService-Info.plist on iOS)
    try {
      // Check if Firebase is already initialized to avoid duplicate app error
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp(
          options: kIsWeb || defaultTargetPlatform == TargetPlatform.android
              ? const FirebaseOptions(
                  apiKey: "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI",
                  authDomain: "rozana-app-customer-b2c.firebaseapp.com",
                  projectId: "rozana-app-customer-b2c",
                  storageBucket: "rozana-app-customer-b2c.firebasestorage.app",
                  messagingSenderId: "211080965257",
                  appId: "1:211080965257:web:3b917158271e9aa422aa58",
                  measurementId: "G-QQS02K1LXM")
              : null,
        );
        LogMessage.p('Firebase initialized successfully', color: Colors.green);

        // Configure Firebase Auth verification settings
        final verificationService = AppVerificationService();
        verificationService.configureFirebaseAuth();
      } else {
        LogMessage.p('Firebase already initialized, skipping initialization',
            color: Colors.orange);
      }
    } catch (e) {
      LogMessage.p('Firebase initialization failed: $e', color: Colors.red);
      // Don't rethrow if it's a duplicate app error, as the app can still function
      if (!e.toString().contains('duplicate-app')) {
        rethrow;
      }
    }
  }

  /// Get the appropriate API base URL based on environment
  static String get baseUrl {
    switch (environment) {
      case Environment.development:
        return BaseUrl.devUrl;
      case Environment.staging:
        return BaseUrl.stagingUrl;
      case Environment.production:
        return BaseUrl.productionUrl;
    }
  }
}
