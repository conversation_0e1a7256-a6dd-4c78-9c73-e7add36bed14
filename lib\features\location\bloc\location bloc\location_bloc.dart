export 'location_event.dart';
export 'location_state.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../../../data/models/adress_model.dart';
import '../../services/adress_services.dart';
import '../../services/locaion_services.dart';
import 'location_event.dart';
import 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final AddressService addressService;
  final LocationService locationService;

  LocationBloc({
    required this.addressService,
    required this.locationService,
  }) : super(const LocationState.initial()) {
    on<LocationEvent>(
      (event, emit) => event.map(
        started: (_) => _onStarted(emit),
        refreshLocation: (e) => _onRefreshLocation(emit),
        // getCurrentLocation: (e) => _onGetCurrentLocation(emit), 

        // getAddressFromCoordinates: (e) => _onGetAddressFromCoordinates(e.latitude, e.longitude, emit),
      ),
    );
  }

  Future<void> _onStarted(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final defaultAddress = await addressService.getDefaultAddress();
      if (defaultAddress != null) {
        emit(LocationState.loaded(defaultAddress));
      } else {
        add(const LocationEvent.refreshLocation());
      }
    } catch (e) {
      emit(LocationState.error('Failed to load default location.'));
    }
  }

  Future<void> _onRefreshLocation(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      final savedAddresses = await addressService.getAllAddresses();
      final currentPosition = await locationService.getCurrentPosition();

      if (currentPosition != null) {
        if (savedAddresses.isEmpty) {
          final placemarks = await locationService.getAddressFromCoordinates(
            currentPosition.latitude,
            currentPosition.longitude,
          );
          if (placemarks != null && placemarks.isNotEmpty) {
            final address =
                _createTempAddress(currentPosition, placemarks.first);
            emit(LocationState.loaded(address));
            return;
          }
        } else {
          AddressModel? closest;
          double minDist = double.infinity;

          for (final addr in savedAddresses) {
            final dist = Geolocator.distanceBetween(
              currentPosition.latitude,
              currentPosition.longitude,
              (addr.latitude ?? 0.0).toDouble(),
              (addr.longitude ?? 0.0).toDouble(),
            );
            if (dist < minDist) {
              minDist = dist;
              closest = addr;
            }
          }

          if (closest != null) {
            if (!(closest.isDefault ?? false)) {
              await addressService.setDefaultAddress(closest.id ?? '');
            }
            emit(LocationState.loaded(closest));
            return;
          }
        }
      }

      final fallback = await addressService.getDefaultAddress();
      if (fallback != null) {
        emit(LocationState.loaded(fallback));
      } else {
        emit(const LocationState.error('No address available.'));
      }
    } catch (e) {
      emit(LocationState.error('Error while refreshing location.'));
    }
  }

  AddressModel _createTempAddress(Position pos, Placemark pm) {
    final fullAddress = [
      pm.street,
      pm.subLocality,
      pm.locality,
      pm.administrativeArea,
      pm.postalCode
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    final addressLine1 = [
      pm.street,
      pm.subLocality,
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    return AddressModel(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: pm.locality ?? '',
      state: pm.administrativeArea ?? '',
      pincode: pm.postalCode ?? '',
      latitude: pos.latitude,
      longitude: pos.longitude,
      addressType: 'current',
      isDefault: true,
    );
  }

//   Future<void> _onGetCurrentLocation(Emitter<LocationState> emit) async {
//   emit(const LocationState.loading());
//   try {
//     final position = await locationService.getCurrentPosition();
//     if (position != null) {
//       final placemarks = await locationService.getAddressFromCoordinates(
//         position.latitude,
//         position.longitude,
//       );
//       if (placemarks != null && placemarks.isNotEmpty) {
//         final address = _createTempAddress(position, placemarks.first);
//         emit(LocationState.loaded(address));
//         return;
//       }
//     }
//     emit(const LocationState.error('Failed to get current location.'));
//   } catch (e) {
//     emit(const LocationState.error('Failed to get current location.'));
//   }
// }

// Future<void> _onGetAddressFromCoordinates(
//     double latitude,
//     double longitude,
//     Emitter<LocationState> emit,
//   ) async {
//     emit(const LocationState.loading());

//     try {
//       final placemarks = await addressService.getAddressFromCoordinates(
//         latitude,
//         longitude,
//       );

//       if (placemarks != null && placemarks.isNotEmpty) {
//         final placemark = placemarks.first;

//         final address = AddressModel(
//           id: 'coord_${DateTime.now().millisecondsSinceEpoch}',
//           fullAddress: _buildAddressString(placemark),
//           addressLine1: [
//             placemark.street,
//             placemark.subLocality,
//           ].where((e) => (e ?? '').isNotEmpty).join(', '),
//           city: placemark.locality ?? '',
//           state: placemark.administrativeArea ?? '',
//           pincode: placemark.postalCode ?? '',
//           latitude: latitude,
//           longitude: longitude,
//           addressType: 'map_pin',
//           isDefault: false,
//         );

//         emit(LocationState.loaded(address));
//       } else {
//         emit(const LocationState.error('Address not found.'));
//       }
//     } catch (e) {
//       emit(const LocationState.error('Failed to get address.'));
//     }
//   }

//   String _buildAddressString(Placemark placemark) {
//     List<String> addressParts = [];

//     if (placemark.street != null && placemark.street!.isNotEmpty) {
//       addressParts.add(placemark.street!);
//     }
//     if (placemark.locality != null && placemark.locality!.isNotEmpty) {
//       addressParts.add(placemark.locality!);
//     }
//     if (placemark.administrativeArea != null &&
//         placemark.administrativeArea!.isNotEmpty) {
//       addressParts.add(placemark.administrativeArea!);
//     }
//     if (placemark.postalCode != null && placemark.postalCode!.isNotEmpty) {
//       addressParts.add(placemark.postalCode!);
//     }

//     return addressParts.isNotEmpty
//         ? addressParts.join(', ')
//         : 'Unknown location';
//   }



}
