class BaseUrl {
  static const String localHostUrl = "";

  static const String devUrl = "https://rozana-oms.headrun.com/";

  static const String stagingUrl = "https://rozana-oms.headrun.com/";

  static const String productionUrl = "https://rozana-oms.headrun.com/";
}

class EndUrl {
  //auth
  static const String loginUser = 'api/sign-in';
  static const String registerUser = 'api/register';
  static const String forgotPassword = 'api/forgot-password';

  //orders
  static const String createOrder = 'create_order';
  static const String getOrderHistory = 'api/orders/history';
  static const String getOrderDetails = 'api/orders'; // append /{orderId}
  static const String cancelOrder = 'api/orders'; // append /{orderId}/cancel
}
